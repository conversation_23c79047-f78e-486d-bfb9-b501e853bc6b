import { router } from 'expo-router';
import React, { useState } from 'react';

import { ThreeXUIConfigForm } from '@/components/3x-ui';
import { SUIConfigForm } from '@/components/s-ui';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { XUIConfigForm } from '@/components/x-ui';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { ConfigFormData, ConfigType, configTypes } from '@/lib/types';
import { Modal, SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Label } from '~/components/ui/label';

export default function AddConfigScreen() {
  const { t } = useTranslation();
  const { addConfig } = useAppStore();
  const [selectedType, setSelectedType] = useState<ConfigType | null>(null);
  const [showConfigForm, setShowConfigForm] = useState(false);

  const handleTypeSelect = (type: ConfigType) => {
    setSelectedType(type);
    setShowConfigForm(true);
  };

  const handleCloseForm = () => {
    setShowConfigForm(false);
    setSelectedType(null);
  };

  const handleFormSubmit = async (formData: ConfigFormData) => {
    if (!selectedType) return;

    try {
      // 使用store的addConfig方法
      await addConfig(formData, selectedType);

      // 关闭模态框并返回主页
      setShowConfigForm(false);
      setSelectedType(null);
      router.back();
    } catch (error) {
      console.error('Failed to add config:', error);
      // 这里可以添加错误提示
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.typeList}>
          {configTypes.map((configType, index) => (
            <View key={configType}>
              
              <TouchableOpacity
                style={styles.typeItem}
                onPress={() => handleTypeSelect(configType)}
              >
                <Text className="text-xl font-semibold">
                  {t(`configTypes.${configType}`)}
                </Text>
              </TouchableOpacity>
              <View style={styles.separator} />
            </View>
          ))}
        </View>
      </ScrollView>

      {/* 配置表单模态框 */}
      <Modal
        visible={showConfigForm}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCloseForm}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Label onPress={handleCloseForm}>{t('common.cancel')}</Label>
            <Text className="text-lg font-semibold">
              {selectedType && t('addConfig.title')} - {selectedType?.toUpperCase()}
            </Text>
            <View style={{ width: 60 }} />
          </View>

          <View style={styles.modalContent}>
            {selectedType === 's-ui' && (
              <SUIConfigForm
                onSubmit={handleFormSubmit}
                onCancel={handleCloseForm}
              />
            )}
            {selectedType === 'x-ui' && (
              <XUIConfigForm
                onSubmit={handleFormSubmit}
                onCancel={handleCloseForm}
              />
            )}
            {selectedType === '3x-ui' && (
              <ThreeXUIConfigForm
                onSubmit={handleFormSubmit}
                onCancel={handleCloseForm}
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    padding: 0,
  },
  header: {
    marginBottom: 24,
  },
  typeList: {
    flex: 1,
  },
  typeItem: {
    paddingVertical: 20,
    paddingHorizontal: 16,
    backgroundColor: 'white',
  },
  separator: {
    height: 1,
    backgroundColor: '#e5e5e5',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  modalContent: {
    flex: 1,
  },
});
