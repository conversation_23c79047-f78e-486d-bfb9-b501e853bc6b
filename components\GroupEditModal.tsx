import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Text } from '@/components/ui/text';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { getGroupDisplayName, isGroupEditable } from '@/lib/groupUtils';
import { useAppStore } from '@/lib/store';
import { Group } from '@/lib/types';
import { ChevronDown, ChevronUp, Edit3, Plus, Trash2 } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { Alert, Modal, SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';

interface GroupEditModalProps {
  visible: boolean;
  onClose: () => void;
}

export function GroupEditModal({ visible, onClose }: GroupEditModalProps) {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const backgroundColor = useThemeColor({}, 'background');

  // 根据主题设置颜色
  const cardBackgroundColor = colorScheme === 'dark' ? '#1C1C1E' : '#f9f9f9';
  const borderColor = useThemeColor({}, 'border');
  const iconColor = colorScheme === 'dark' ? '#ffffff' : '#000000';
  const destructiveColor = colorScheme === 'dark' ? '#ff6b6b' : '#dc2626';

  const { groups, addGroup, updateGroup, deleteGroup, reorderGroups } = useAppStore();
  const [editableGroups, setEditableGroups] = useState<Group[]>([]);
  const [showAddInput, setShowAddInput] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');
  const [editingGroupId, setEditingGroupId] = useState<string | null>(null);
  const [editingGroupName, setEditingGroupName] = useState('');

  // 显示所有分组，包括 'default' 分组（全部分组）
  useEffect(() => {
    // 按 order 排序
    const sortedGroups = [...groups].sort((a, b) => a.order - b.order);
    setEditableGroups(sortedGroups);
  }, [groups]);

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    
    const newGroups = [...editableGroups];
    [newGroups[index], newGroups[index - 1]] = [newGroups[index - 1], newGroups[index]];
    
    // 更新 order
    const updatedGroups = newGroups.map((group, idx) => ({
      ...group,
      order: idx, // 从0开始
    }));
    
    setEditableGroups(updatedGroups);
  };

  const handleMoveDown = (index: number) => {
    if (index === editableGroups.length - 1) return;
    
    const newGroups = [...editableGroups];
    [newGroups[index], newGroups[index + 1]] = [newGroups[index + 1], newGroups[index]];
    
    // 更新 order
    const updatedGroups = newGroups.map((group, idx) => ({
      ...group,
      order: idx, // 从0开始
    }));
    
    setEditableGroups(updatedGroups);
  };

  const handleStartEdit = (group: Group) => {
    if (!isGroupEditable(group.id)) {
      Alert.alert(t('common.error'), t('groups.cannotRenameDefault'));
      return;
    }
    setEditingGroupId(group.id);
    setEditingGroupName(group.name);
  };

  const handleSaveEdit = async () => {
    if (!editingGroupId || !editingGroupName.trim()) return;
    
    await updateGroup(editingGroupId, { name: editingGroupName.trim() });
    setEditingGroupId(null);
    setEditingGroupName('');
  };

  const handleCancelEdit = () => {
    setEditingGroupId(null);
    setEditingGroupName('');
  };

  const handleDelete = (group: Group) => {
    if (!isGroupEditable(group.id)) {
      Alert.alert(t('common.error'), t('groups.cannotDeleteDefault'));
      return;
    }

    Alert.alert(
      t('groups.deleteGroup'),
      t('groups.deleteConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { 
          text: t('common.delete'), 
          style: 'destructive',
          onPress: () => deleteGroup(group.id)
        },
      ]
    );
  };

  const handleAddGroup = async () => {
    if (!newGroupName.trim()) return;
    
    await addGroup(newGroupName.trim());
    setNewGroupName('');
    setShowAddInput(false);
  };

  const handleSave = async () => {
    // 保存分组顺序
    await reorderGroups(editableGroups);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        {/* 头部 */}
        <View style={[styles.header, { borderBottomColor: borderColor }]}>
          <Button variant="ghost" onPress={onClose}>
            <Text>{t('common.cancel')}</Text>
          </Button>
          <Text className="text-lg font-semibold">{t('groups.title')}</Text>
          <Button variant="ghost" onPress={handleSave}>
            <Text>{t('common.save')}</Text>
          </Button>
        </View>

        {/* 分组列表 */}
        <ScrollView style={styles.content}>
          {editableGroups.map((group, index) => (
            <View key={group.id} style={[styles.groupItem, { backgroundColor: cardBackgroundColor, borderColor }]}>
              <View style={styles.groupInfo}>
                {editingGroupId === group.id ? (
                  <View style={styles.editContainer}>
                    <Input
                      value={editingGroupName}
                      onChangeText={setEditingGroupName}
                      placeholder={t('groups.newGroupName')}
                      style={styles.editInput}
                      autoFocus
                    />
                    <View style={styles.editActions}>
                      <Button variant="ghost" size="sm" onPress={handleSaveEdit}>
                        <Text>{t('common.save')}</Text>
                      </Button>
                      <Button variant="ghost" size="sm" onPress={handleCancelEdit}>
                        <Text>{t('common.cancel')}</Text>
                      </Button>
                    </View>
                  </View>
                ) : (
                  <Text className="text-lg font-medium">{getGroupDisplayName(group, t)}</Text>
                )}
              </View>

              <View style={styles.groupActions}>
                {/* 上下移动按钮 */}
                <View style={styles.moveButtons}>
                  {index > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onPress={() => handleMoveUp(index)}
                      style={styles.moveButton}
                    >
                      <ChevronUp size={16} color={iconColor} />
                    </Button>
                  )}
                  {index < editableGroups.length - 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onPress={() => handleMoveDown(index)}
                      style={styles.moveButton}
                    >
                      <ChevronDown size={16} color={iconColor} />
                    </Button>
                  )}
                </View>

                {/* 编辑和删除按钮 */}
                {editingGroupId !== group.id && isGroupEditable(group.id) && (
                  <View style={styles.editDeleteButtons}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onPress={() => handleStartEdit(group)}
                        style={styles.actionButton}
                      >
                        <Edit3 size={16} color={iconColor} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onPress={() => handleDelete(group)}
                        style={styles.actionButton}
                      >
                        <Trash2 size={16} color={destructiveColor} />
                      </Button>
                  </View>
                )}
              </View>
            </View>
          ))}

          {/* 添加新分组 */}
          {showAddInput ? (
            <View style={[styles.addContainer, { backgroundColor: cardBackgroundColor, borderColor }]}>
              <Input
                value={newGroupName}
                onChangeText={setNewGroupName}
                placeholder={t('groups.enterGroupName')}
                style={styles.addInput}
                autoFocus
              />
              <View style={styles.addActions}>
                <Button variant="default" size="sm" onPress={handleAddGroup}>
                  <Text>{t('common.add')}</Text>
                </Button>
                <Button variant="ghost" size="sm" onPress={() => {
                  setShowAddInput(false);
                  setNewGroupName('');
                }}>
                  <Text>{t('common.cancel')}</Text>
                </Button>
              </View>
            </View>
          ) : (
            <Button
              variant="secondary"
              onPress={() => setShowAddInput(true)}
              style={styles.addButton}
            >
              <Plus size={16} color={iconColor} />
              <Text style={styles.addButtonText}>{t('groups.addGroup')}</Text>
            </Button>
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    // borderBottomColor will be set dynamically
  },
  content: {
    flex: 1,
    padding: 16,
  },
  groupItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    // backgroundColor and borderColor will be set dynamically
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  groupInfo: {
    flex: 1,
  },
  editContainer: {
    flex: 1,
  },
  editInput: {
    marginBottom: 8,
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  groupActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moveButtons: {
    flexDirection: 'column',
  },
  moveButton: {
    padding:8,
  },
  editDeleteButtons: {
    flexDirection: 'row',
    gap: 4,
    marginLeft:4
  },
  actionButton: {
    padding: 8,
  },
  addContainer: {
    padding: 16,
    // backgroundColor and borderColor will be set dynamically
    borderRadius: 8,
    borderWidth: 1,
    marginTop: 8,
  },
  addInput: {
    marginBottom: 12,
  },
  addActions: {
    flexDirection: 'row',
    gap: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    gap: 8,
  },
  addButtonText: {
    marginLeft: 8,
  },
});
