import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

interface SSLPinningOptions {
  method?: HTTPMethod;
  timeoutInterval?: number;
  pkPinning?: boolean;
  sslPinning: {
    certs: string[];
  };
  headers?: Record<string, string>;
  body?: string | object;
}

/**
 * 封装的fetch函数，支持可选的SSL证书固定
 * @param url 请求URL
 * @param options 请求选项
 * @param certs 可选的证书数组，如果提供则使用SSL固定
 * @returns Promise<any> 返回响应对象
 */
export async function customFetch(
  url: string,
  options: RequestInit = {},
  certs?: string[]
): Promise<any> {
  if (certs && certs.length > 0) {
    // 使用SSL固定的fetch
    const { fetch: sslFetch } = await import('react-native-ssl-pinning');

    const method = (options.method?.toUpperCase() as HTTPMethod) || 'GET';

    const sslOptions: SSLPinningOptions = {
      method: method,
      timeoutInterval: 10000, // 默认10秒超时
      pkPinning: true,
      sslPinning: {
        certs: certs
      },
      headers: {
        'Accept': 'application/json; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'e_platform': 'mobile',
        ...(options.headers as Record<string, string> || {})
      },
      body: options.body ? (typeof options.body === 'string' ? options.body : JSON.stringify(options.body)) : undefined
    };

    return sslFetch(url, sslOptions);
  } else {
    // 使用原生fetch
    return fetch(url, {
      ...options,
      headers: {
        'Accept': 'application/json; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'e_platform': 'mobile',
        ...options.headers
      }
    });
  }
}
