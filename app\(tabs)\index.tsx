import { GroupEditModal } from '@/components/GroupEditModal';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { getGroupDisplayName } from '@/lib/groupUtils';
import { useAppStore } from '@/lib/store';
import { router } from 'expo-router';
import { Edit3, Plus } from 'lucide-react-native';
import React, { useState } from 'react';
import { Alert, SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';

export default function HomeScreen() {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const backgroundColor = useThemeColor({}, 'background');

  // 根据主题设置颜色
  const cardBackgroundColor = colorScheme === 'dark' ? '#1C1C1E' : '#f9f9f9';
  const borderColor = colorScheme === 'dark' ? '#2C2C2E' : '#e5e5e5';
  const mutedBackgroundColor = colorScheme === 'dark' ? '#2C2C2E' : '#f0f0f0';
  const iconColor = colorScheme === 'dark' ? '#ffffff' : '#000000';
  const foregroundColor = colorScheme === 'dark' ? '#ffffff' : '#000000';

  const { groups, selectedGroupId, setSelectedGroup, getFilteredConfigs, clearAllData } = useAppStore();
  const [showGroupEdit, setShowGroupEdit] = useState(false);

  const configs = getFilteredConfigs();

  // 按order排序分组
  const sortedGroups = [...groups].sort((a, b) => a.order - b.order);

  const handleAddConfig = () => {
    router.push('/add-config');
  };

  const handleSettings = () => {
    router.push('/(tabs)/settings');
  };

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroup(groupId);
  };

  const handleEditGroups = () => {
    setShowGroupEdit(true);
  };

  const handleCloseGroupEdit = () => {
    setShowGroupEdit(false);
  };

  const handleClearData = () => {
    Alert.alert(
      '清空数据',
      '确定要清空所有数据吗？这将删除所有配置和分组，并重置应用到初始状态。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          style: 'destructive',
          onPress: () => clearAllData()
        },
      ]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 顶部导航栏 - 删除底部横线 */}
      <View style={styles.header}>
        <Text className="text-2xl font-bold">{t('home.title')}</Text>
        <View style={styles.headerButtons}>
          <Button
            variant="ghost"
            size="icon"
            onPress={handleAddConfig}
          >
            <Plus strokeWidth={2.5} size={25} color={iconColor} />
          </Button>
        </View>
      </View>

      {/* 分组按钮组 - 删除底部横线 */}
      <View style={styles.groupSection}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.groupScrollView}
          contentContainerStyle={styles.groupContainer}
        >
          {/* 分组按钮 */}
          {sortedGroups.map((group) => (
            <Button
              key={group.id}
              variant={selectedGroupId === group.id ? 'default' : 'secondary'}
              size="sm"
              onPress={() => handleGroupSelect(group.id)}
              style={styles.groupButton}
            >
              <Text>{getGroupDisplayName(group, t)}</Text>
            </Button>
          ))}

          {/* 编辑按钮 */}
          <Button
            variant="secondary"
            size="sm"
            onPress={handleEditGroups}
            style={styles.editButton}
          >
            <Edit3 size={16} color={foregroundColor} />
          </Button>

          {/* 临时清空数据按钮 - 仅用于开发调试 */}
          <Button
            variant="destructive"
            size="sm"
            onPress={handleClearData}
            style={styles.editButton}
          >
            <Text style={{ fontSize: 12 }}>清空</Text>
          </Button>
        </ScrollView>
      </View>

      {/* 配置列表区域 */}
      <ScrollView style={styles.configList}>
        {configs.length === 0 ? (
          <View style={styles.emptyState}>
            <Text className="text-lg text-muted-foreground text-center">
              {t('home.noConfigurations')}
            </Text>
            <Text className="text-sm text-muted-foreground text-center mt-2">
              {t('home.addFirstConfig')}
            </Text>
            <Button
              variant="default"
              onPress={handleAddConfig}
              style={styles.addFirstButton}
            >
              <Text>{t('home.addConfiguration')}</Text>
            </Button>
          </View>
        ) : (
          <View style={styles.configGrid}>
            {configs.map((config) => (
              <View key={config.id} style={[styles.configCard, { backgroundColor: cardBackgroundColor, borderColor }]}>
                <Text className="text-lg font-semibold">{config.name}</Text>
                <Text className="text-sm text-muted-foreground">{config.type.toUpperCase()}</Text>
                <Text className="text-sm text-muted-foreground">{config.url}</Text>
                {/* 这里将来会显示监控状态 */}
                <View style={[styles.statusPlaceholder, { backgroundColor: mutedBackgroundColor }]}>
                  <Text className="text-xs text-muted-foreground">监控状态 - 待实现</Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      {/* 分组编辑模态框 */}
      <GroupEditModal
        visible={showGroupEdit}
        onClose={handleCloseGroupEdit}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    // 删除底部横线
    // borderBottomWidth: 1,
    // borderBottomColor: '#e5e5e5',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  settingsButton: {
    marginRight: 8,
  },
  groupSection: {
    paddingVertical: 12,
    // 删除底部横线
    // borderBottomWidth: 1,
    // borderBottomColor: '#e5e5e5',
  },
  groupScrollView: {
    paddingHorizontal: 16,
  },
  groupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  groupButton: {
    marginRight: 8,
  },
  editButton: {
    marginLeft: 8,
  },
  configList: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  addFirstButton: {
    marginTop: 16,
  },
  configGrid: {
    gap: 12,
  },
  configCard: {
    // backgroundColor and borderColor will be set dynamically
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  statusPlaceholder: {
    marginTop: 8,
    padding: 8,
    // backgroundColor will be set dynamically
    borderRadius: 4,
  },
});
