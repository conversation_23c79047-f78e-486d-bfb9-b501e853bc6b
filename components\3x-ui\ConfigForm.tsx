import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Text } from '@/components/ui/text';
import { Textarea } from '@/components/ui/textarea';
import { useTranslation } from '@/hooks/useTranslation';
import { getGroupDisplayName } from '@/lib/groupUtils';
import { useAppStore } from '@/lib/store';
import { ConfigFormData, ProtocolType } from '@/lib/types';
import { AlertTriangle, HelpCircle } from 'lucide-react-native';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, View, Alert as RNAlert } from 'react-native';

interface ThreeXUIConfigFormProps {
  initialData?: Partial<ConfigFormData>;
  onSubmit: (data: ConfigFormData) => void;
}

export default function ThreeXUIConfigForm({
  initialData,
  onSubmit,
}: ThreeXUIConfigFormProps) {
  const { t } = useTranslation();
  const { groups } = useAppStore();
  
  // 过滤掉"全部"分组
  const availableGroups = groups.filter(group => group.id !== 'all');

  const [formData, setFormData] = useState<ConfigFormData>({
    name: initialData?.name || '',
    url: initialData?.url || '',
    protocol: initialData?.protocol || 'https',
    groupId: initialData?.groupId || [],
    username: initialData?.username || '',
    password: initialData?.password || '',
    cert: initialData?.cert || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleProtocolChange = (protocol: ProtocolType) => {
    setFormData(prev => ({ ...prev, protocol }));
    // 清除证书字段的错误
    if (errors.cert) {
      setErrors(prev => ({ ...prev, cert: '' }));
    }
  };

  const handleInputChange = (field: keyof ConfigFormData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleGroupToggle = (groupId: string) => {
    const currentGroupIds = Array.isArray(formData.groupId) ? formData.groupId : [formData.groupId].filter(Boolean);

    if (currentGroupIds.includes(groupId)) {
      // 移除分组
      const newGroupIds = currentGroupIds.filter(id => id !== groupId);
      handleInputChange('groupId', newGroupIds);
    } else {
      // 添加分组
      handleInputChange('groupId', [...currentGroupIds, groupId]);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('validation.required');
    }

    if (!formData.url.trim()) {
      newErrors.url = t('validation.required');
    } else {
      // 简单的URL验证
      try {
        new URL(formData.url);
      } catch {
        newErrors.url = t('validation.invalidUrl');
      }
    }

    if (!formData.username?.trim()) {
      newErrors.username = t('validation.required');
    }

    if (!formData.password?.trim()) {
      newErrors.password = t('validation.required');
    }

    const groupIds = Array.isArray(formData.groupId) ? formData.groupId : [formData.groupId].filter(Boolean);
    if (groupIds.length === 0) {
      newErrors.groupId = t('validation.required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const showCertAlert = () => {
    RNAlert.alert(
      t('configForm.cert'),
      t('configForm.certTooltip'),
      [{ text: t('common.ok'), style: 'default' }]
    );
  };

  const handleSubmit = () => {
    if (validateForm()) {
      // 确保groupId是单个字符串（取第一个选中的分组）
      const groupIds = Array.isArray(formData.groupId) ? formData.groupId : [formData.groupId].filter(Boolean);
      const submitData = {
        ...formData,
        groupId: groupIds[0] || '', // 取第一个分组作为主分组
      };
      onSubmit(submitData);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.form}>
        {/* 配置名称 */}
        <View style={styles.field}>
          <Label>{t('configForm.name')}</Label>
          <Input
            value={formData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            placeholder={t('configForm.namePlaceholder')}
            style={errors.name ? styles.errorInput : undefined}
          />
          {errors.name && (
            <Text style={styles.errorText}>{errors.name}</Text>
          )}
        </View>

        {/* 协议选择 */}
        <View style={styles.field}>
          <Label>{t('configForm.protocol')}</Label>
          <View style={styles.protocolButtons}>
            <Button
              variant={formData.protocol === 'https' ? 'default' : 'secondary'}
              size="sm"
              onPress={() => handleProtocolChange('https')}
              style={styles.protocolButton}
            >
              <Text>HTTPS</Text>
            </Button>
            <Button
              variant={formData.protocol === 'http' ? 'default' : 'secondary'}
              size="sm"
              onPress={() => handleProtocolChange('http')}
              style={styles.protocolButton}
            >
              <Text>HTTP</Text>
            </Button>
          </View>
        </View>

        {/* HTTP 安全警告 */}
        {formData.protocol === 'http' && (
          <Alert variant="destructive" icon={AlertTriangle} style={styles.alert}>
            <AlertTitle>{t('configForm.httpWarningTitle')}</AlertTitle>
            <AlertDescription>
              {t('configForm.httpWarning')}
            </AlertDescription>
          </Alert>
        )}

        {/* URL */}
        <View style={styles.field}>
          <Label>{t('configForm.url')}</Label>
          <Input
            value={formData.url}
            onChangeText={(value) => handleInputChange('url', value)}
            placeholder="example.com:54321"
            style={errors.url ? styles.errorInput : undefined}
          />
          {errors.url && (
            <Text style={styles.errorText}>{errors.url}</Text>
          )}
        </View>

        {/* 用户名 */}
        <View style={styles.field}>
          <Label>{t('configForm.username')}</Label>
          <Input
            value={formData.username}
            onChangeText={(value) => handleInputChange('username', value)}
            placeholder={t('configForm.usernamePlaceholder')}
            style={errors.username ? styles.errorInput : undefined}
          />
          {errors.username && (
            <Text style={styles.errorText}>{errors.username}</Text>
          )}
        </View>

        {/* 密码 */}
        <View style={styles.field}>
          <Label>{t('configForm.password')}</Label>
          <Input
            value={formData.password}
            onChangeText={(value) => handleInputChange('password', value)}
            placeholder={t('configForm.passwordPlaceholder')}
            secureTextEntry
            style={errors.password ? styles.errorInput : undefined}
          />
          {errors.password && (
            <Text style={styles.errorText}>{errors.password}</Text>
          )}
        </View>

        {/* HTTPS 证书 */}
        {formData.protocol === 'https' && (
          <View style={styles.field}>
            <View style={styles.labelWithTooltip}>
              <Label>{t('configForm.cert')}</Label>
              <HelpCircle onPress={showCertAlert} size={16} color="#666" />
            </View>
            <Textarea
              value={formData.cert}
              onChangeText={(value) => handleInputChange('cert', value)}
              placeholder={t('configForm.certPlaceholder')}
              style={styles.textarea}
              multiline
              numberOfLines={4}
            />
          </View>
        )}

        {/* 分组选择 */}
        <View style={styles.field}>
          <Label>{t('configForm.group')}</Label>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.groupScroll}
          >
            <View style={styles.groupButtons}>
              {availableGroups.map((group) => {
                const currentGroupIds = Array.isArray(formData.groupId) ? formData.groupId : [formData.groupId].filter(Boolean);
                const isSelected = currentGroupIds.includes(group.id);

                return (
                  <Button
                    key={group.id}
                    variant={isSelected ? 'default' : 'secondary'}
                    size="sm"
                    onPress={() => handleGroupToggle(group.id)}
                  >
                    <Text>{getGroupDisplayName(group, t)}</Text>
                  </Button>
                );
              })}
            </View>
          </ScrollView>
          {errors.groupId && (
            <Text style={styles.errorText}>{errors.groupId}</Text>
          )}
        </View>

        </View>
      </ScrollView>

      {/* 固定在底部的提交按钮 */}
      <View style={styles.bottomActions}>
        <Button
          variant="default"
          onPress={handleSubmit}
          style={styles.submitButton}
        >
          <Text>{t('common.save')}</Text>
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  form: {
    padding: 16,
    gap: 16,
    paddingBottom: 100, // 为底部按钮留出空间
  },
  field: {
    gap: 8,
  },
  protocolButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  protocolButton: {
    flex: 1,
  },
  alert: {
    marginVertical: 8,
  },
  alertText: {
    fontSize: 14,
  },
  labelWithTooltip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  textarea: {
    minHeight: 100,
  },
  groupScroll: {
    maxHeight: 50,
  },
  groupButtons: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 4,
  },
  groupButton: {
    minWidth: 80,
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E5E7',
  },
  submitButton: {
    width: '100%',
  },
  errorInput: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
  },
});
